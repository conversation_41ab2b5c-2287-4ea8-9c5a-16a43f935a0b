<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sacred Geometry Music Visualizer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: radial-gradient(circle at center, #0a0a0a, #000);
            overflow: hidden;
            font-family: 'Arial', sans-serif;
            color: #fff;
        }

        canvas {
            display: block;
            background: transparent;
        }

        .controls {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 100;
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .control-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-size: 12px;
            color: #ccc;
        }

        input[type="file"], select, button {
            width: 100%;
            padding: 8px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 5px;
            color: #fff;
            font-size: 12px;
        }

        button {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        button:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }

        input[type="range"] {
            width: 100%;
            margin: 5px 0;
        }

        .info {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            font-size: 12px;
            max-width: 250px;
        }

        .sacred-symbol {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            pointer-events: none;
            opacity: 0.1;
            font-size: 200px;
            color: #fff;
            z-index: 1;
        }
    </style>
</head>
<body>
    <div class="sacred-symbol">⚛</div>
    
    <div class="controls">
        <div class="control-group">
            <label>Audio File</label>
            <input type="file" id="audioFile" accept="audio/*">
        </div>
        
        <div class="control-group">
            <button id="playPause">Play / Pause</button>
        </div>
        
        <div class="control-group">
            <label>Zoom Level: <span id="zoomValue">1.0</span></label>
            <input type="range" id="zoomLevel" min="0.1" max="5.0" step="0.1" value="1.0">
        </div>
        
        <div class="control-group">
            <label>Geometry Complexity: <span id="complexityValue">5</span></label>
            <input type="range" id="complexity" min="3" max="12" value="5">
        </div>
        
        <div class="control-group">
            <label>Sensitivity: <span id="sensitivityValue">50</span></label>
            <input type="range" id="sensitivity" min="1" max="100" value="50">
        </div>
        
        <div class="control-group">
            <button id="autoZoom">Auto Zoom</button>
        </div>
    </div>

    <div class="info">
        <strong>🔮 Sacred Geometry Visualizer</strong><br>
        Nested circles with music-reactive bars<br>
        Zoom in to reveal infinite patterns<br>
        <br>
        <strong>Features:</strong><br>
        • Flower of Life geometry<br>
        • Fractal zoom patterns<br>
        • Audio-reactive sacred forms<br>
        • Golden ratio proportions
    </div>

    <canvas id="canvas"></canvas>

    <script>
        class SacredGeometryVisualizer {
            constructor() {
                this.canvas = document.getElementById('canvas');
                this.ctx = this.canvas.getContext('2d');
                this.audio = new Audio();
                this.audioContext = null;
                this.analyser = null;
                this.dataArray = null;
                this.source = null;
                
                this.time = 0;
                this.zoomLevel = 1.0;
                this.autoZoomEnabled = false;
                this.goldenRatio = 1.618033988749;
                
                this.settings = {
                    complexity: 5,
                    sensitivity: 50,
                    zoomSpeed: 0.01
                };
                
                this.init();
                this.setupEventListeners();
                this.animate();
            }
            
            init() {
                this.resizeCanvas();
                window.addEventListener('resize', () => this.resizeCanvas());
            }
            
            resizeCanvas() {
                this.canvas.width = window.innerWidth;
                this.canvas.height = window.innerHeight;
            }
            
            setupEventListeners() {
                const audioFile = document.getElementById('audioFile');
                const playPause = document.getElementById('playPause');
                const zoomSlider = document.getElementById('zoomLevel');
                const complexitySlider = document.getElementById('complexity');
                const sensitivitySlider = document.getElementById('sensitivity');
                const autoZoomBtn = document.getElementById('autoZoom');
                
                audioFile.addEventListener('change', (e) => this.loadAudio(e.target.files[0]));
                playPause.addEventListener('click', () => this.togglePlayPause());
                
                zoomSlider.addEventListener('input', (e) => {
                    this.zoomLevel = parseFloat(e.target.value);
                    document.getElementById('zoomValue').textContent = this.zoomLevel.toFixed(1);
                });
                
                complexitySlider.addEventListener('input', (e) => {
                    this.settings.complexity = parseInt(e.target.value);
                    document.getElementById('complexityValue').textContent = this.settings.complexity;
                });
                
                sensitivitySlider.addEventListener('input', (e) => {
                    this.settings.sensitivity = parseInt(e.target.value);
                    document.getElementById('sensitivityValue').textContent = this.settings.sensitivity;
                });
                
                autoZoomBtn.addEventListener('click', () => {
                    this.autoZoomEnabled = !this.autoZoomEnabled;
                    autoZoomBtn.textContent = this.autoZoomEnabled ? 'Stop Auto Zoom' : 'Auto Zoom';
                });
            }
            
            async loadAudio(file) {
                if (!file) return;
                
                try {
                    this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    this.analyser = this.audioContext.createAnalyser();
                    this.analyser.fftSize = 512;
                    this.dataArray = new Uint8Array(this.analyser.frequencyBinCount);
                    
                    this.audio.src = URL.createObjectURL(file);
                    this.source = this.audioContext.createMediaElementSource(this.audio);
                    this.source.connect(this.analyser);
                    this.analyser.connect(this.audioContext.destination);
                    
                    console.log('Audio loaded successfully');
                } catch (error) {
                    console.error('Error loading audio:', error);
                }
            }
            
            togglePlayPause() {
                if (this.audio.paused) {
                    this.audio.play();
                } else {
                    this.audio.pause();
                }
            }
            
            drawSacredGeometry() {
                const centerX = this.canvas.width / 2;
                const centerY = this.canvas.height / 2;
                const baseRadius = Math.min(this.canvas.width, this.canvas.height) / 6;

                // Clear canvas with fade effect
                this.ctx.fillStyle = 'rgba(0, 0, 0, 0.03)';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

                if (!this.dataArray) {
                    // Draw static sacred geometry when no audio
                    this.drawStaticSacredGeometry(centerX, centerY, baseRadius);
                    return;
                }

                this.analyser.getByteFrequencyData(this.dataArray);

                // Calculate frequency bands for different sacred patterns
                const bassRange = this.dataArray.slice(0, this.dataArray.length / 4);
                const midRange = this.dataArray.slice(this.dataArray.length / 4, this.dataArray.length / 2);
                const trebleRange = this.dataArray.slice(this.dataArray.length / 2);

                const bassAvg = bassRange.reduce((a, b) => a + b) / bassRange.length;
                const midAvg = midRange.reduce((a, b) => a + b) / midRange.length;
                const trebleAvg = trebleRange.reduce((a, b) => a + b) / trebleRange.length;

                if (this.autoZoomEnabled) {
                    const totalEnergy = (bassAvg + midAvg + trebleAvg) / 3;
                    this.zoomLevel += (totalEnergy / 255) * this.settings.zoomSpeed;
                    // Create zoom oscillation for more dynamic effect
                    this.zoomLevel += Math.sin(this.time * 0.02) * 0.001;
                    document.getElementById('zoomValue').textContent = this.zoomLevel.toFixed(2);
                }

                // Draw fractal layers with different sacred geometry patterns
                this.drawFractalLayers(centerX, centerY, baseRadius, bassAvg, midAvg, trebleAvg);

                // Draw central mandala
                this.drawCentralMandala(centerX, centerY, baseRadius * 0.3, bassAvg, midAvg, trebleAvg);

                // Draw outer cosmic rings
                this.drawCosmicRings(centerX, centerY, baseRadius, bassAvg, midAvg, trebleAvg);
            }

            drawStaticSacredGeometry(centerX, centerY, baseRadius) {
                // Draw basic Flower of Life pattern when no audio
                this.ctx.strokeStyle = 'rgba(100, 150, 255, 0.3)';
                this.ctx.lineWidth = 1;

                for (let i = 0; i < 6; i++) {
                    const angle = (i / 6) * Math.PI * 2;
                    const x = centerX + Math.cos(angle) * baseRadius * 0.5;
                    const y = centerY + Math.sin(angle) * baseRadius * 0.5;

                    this.ctx.beginPath();
                    this.ctx.arc(x, y, baseRadius * 0.5, 0, Math.PI * 2);
                    this.ctx.stroke();
                }

                // Central circle
                this.ctx.beginPath();
                this.ctx.arc(centerX, centerY, baseRadius * 0.5, 0, Math.PI * 2);
                this.ctx.stroke();
            }
            
            drawFractalLayers(centerX, centerY, baseRadius, bassAvg, midAvg, trebleAvg) {
                // Draw multiple fractal layers that zoom infinitely
                for (let layer = 0; layer < 9; layer++) {
                    const scaleFactor = Math.pow(this.goldenRatio, layer - 4) * this.zoomLevel;
                    const layerRadius = baseRadius * scaleFactor;

                    // Skip layers that are too small or too large
                    if (layerRadius < 10 || layerRadius > Math.max(this.canvas.width, this.canvas.height) * 2) continue;

                    const opacity = Math.max(0.05, Math.min(0.8, 1 - Math.abs(layer - 4) * 0.15));
                    const rotationSpeed = (layer + 1) * 0.005;

                    // Different patterns for different layers
                    if (layer % 3 === 0) {
                        this.drawFlowerOfLifeLayer(centerX, centerY, layerRadius, layer, bassAvg, opacity, rotationSpeed);
                    } else if (layer % 3 === 1) {
                        this.drawSeedOfLifeLayer(centerX, centerY, layerRadius, layer, midAvg, opacity, rotationSpeed);
                    } else {
                        this.drawMetatronsCubeLayer(centerX, centerY, layerRadius, layer, trebleAvg, opacity, rotationSpeed);
                    }
                }
            }

            drawFlowerOfLifeLayer(centerX, centerY, radius, layer, amplitude, opacity, rotationSpeed) {
                const circles = 6 + layer;
                this.ctx.save();
                this.ctx.globalAlpha = opacity;
                this.ctx.translate(centerX, centerY);
                this.ctx.rotate(this.time * rotationSpeed);

                for (let i = 0; i < circles; i++) {
                    const angle = (i / circles) * Math.PI * 2;
                    const x = Math.cos(angle) * radius * 0.4;
                    const y = Math.sin(angle) * radius * 0.4;

                    this.drawMusicReactiveBars(x, y, radius / 4, i, layer, 1, amplitude);
                }

                this.ctx.restore();
            }

            drawSeedOfLifeLayer(centerX, centerY, radius, layer, amplitude, opacity, rotationSpeed) {
                const circles = 7;
                this.ctx.save();
                this.ctx.globalAlpha = opacity;
                this.ctx.translate(centerX, centerY);
                this.ctx.rotate(this.time * rotationSpeed * -1); // Counter-rotate

                // Central circle
                this.drawMusicReactiveBars(0, 0, radius / 5, 0, layer, 1, amplitude);

                // Surrounding circles
                for (let i = 0; i < 6; i++) {
                    const angle = (i / 6) * Math.PI * 2;
                    const x = Math.cos(angle) * radius * 0.3;
                    const y = Math.sin(angle) * radius * 0.3;

                    this.drawMusicReactiveBars(x, y, radius / 6, i + 1, layer, 1, amplitude);
                }

                this.ctx.restore();
            }

            drawMetatronsCubeLayer(centerX, centerY, radius, layer, amplitude, opacity, rotationSpeed) {
                const points = 13; // Sacred number in Metatron's Cube
                this.ctx.save();
                this.ctx.globalAlpha = opacity;
                this.ctx.translate(centerX, centerY);
                this.ctx.rotate(this.time * rotationSpeed * 0.5);

                // Draw the cube vertices with music bars
                for (let i = 0; i < points; i++) {
                    const angle = (i / points) * Math.PI * 2;
                    const layerRadius = (i % 2 === 0) ? radius * 0.5 : radius * 0.3;
                    const x = Math.cos(angle) * layerRadius;
                    const y = Math.sin(angle) * layerRadius;

                    this.drawMusicReactiveBars(x, y, radius / 8, i, layer, 1, amplitude);
                }

                // Draw connecting lines for the cube structure
                this.drawMetatronConnections(radius, amplitude, layer);

                this.ctx.restore();
            }
            
            drawMusicReactiveBars(centerX, centerY, radius, circleIndex, layer, globalOpacity, frequencyData = null) {
                const barsPerCircle = 16 + layer * 2; // More bars for outer layers
                const dataStep = Math.floor(this.dataArray.length / barsPerCircle);

                this.ctx.save();
                this.ctx.translate(centerX, centerY);
                this.ctx.rotate(this.time * 0.003 * (layer + 1) + circleIndex * 0.1);

                for (let i = 0; i < barsPerCircle; i++) {
                    const dataIndex = (i * dataStep + circleIndex * 3) % this.dataArray.length;
                    const amplitude = (frequencyData || this.dataArray[dataIndex]) / 255;
                    const barHeight = amplitude * radius * (this.settings.sensitivity / 50) * (1 + Math.sin(this.time * 0.02 + i) * 0.2);

                    const angle = (i / barsPerCircle) * Math.PI * 2;
                    const innerRadius = radius * 0.2;
                    const outerRadius = innerRadius + barHeight;

                    // Create more dynamic bar shapes
                    const x1 = Math.cos(angle) * innerRadius;
                    const y1 = Math.sin(angle) * innerRadius;
                    const x2 = Math.cos(angle) * outerRadius;
                    const y2 = Math.sin(angle) * outerRadius;

                    // Enhanced color system based on sacred geometry principles
                    const goldenAngle = (i / barsPerCircle) * 137.5; // Golden angle in degrees
                    const hue = (goldenAngle + layer * 45 + this.time * 0.5) % 360;
                    const saturation = 60 + amplitude * 40;
                    const lightness = 30 + amplitude * 50;
                    const opacity = globalOpacity * (0.6 + amplitude * 0.4);

                    // Create gradient bars for more visual appeal
                    const gradient = this.ctx.createLinearGradient(x1, y1, x2, y2);
                    gradient.addColorStop(0, `hsla(${hue}, ${saturation}%, ${lightness}%, ${opacity * 0.3})`);
                    gradient.addColorStop(1, `hsla(${hue}, ${saturation}%, ${lightness}%, ${opacity})`);

                    this.ctx.strokeStyle = gradient;
                    this.ctx.lineWidth = 1 + amplitude * 4;
                    this.ctx.lineCap = 'round';

                    // Enhanced glow effect
                    this.ctx.shadowBlur = 5 + amplitude * 25;
                    this.ctx.shadowColor = `hsla(${hue}, ${saturation}%, ${lightness}%, ${opacity * 0.6})`;

                    this.ctx.beginPath();
                    this.ctx.moveTo(x1, y1);
                    this.ctx.lineTo(x2, y2);
                    this.ctx.stroke();

                    // Add inner glow dots
                    if (amplitude > 0.3) {
                        this.ctx.shadowBlur = 15;
                        this.ctx.fillStyle = `hsla(${hue}, 100%, 80%, ${amplitude * opacity})`;
                        this.ctx.beginPath();
                        this.ctx.arc(x2, y2, amplitude * 2, 0, Math.PI * 2);
                        this.ctx.fill();
                    }
                }

                this.ctx.restore();
            }
            
            drawSacredConnections(centerX, centerY, radius, circles, layer, opacity) {
                this.ctx.save();
                this.ctx.globalAlpha = opacity * 0.3;
                this.ctx.strokeStyle = `hsl(${(layer * 60 + this.time * 0.5) % 360}, 50%, 60%)`;
                this.ctx.lineWidth = 1;
                this.ctx.setLineDash([5, 5]);
                
                // Draw Flower of Life pattern
                for (let i = 0; i < circles; i++) {
                    const angle1 = (i / circles) * Math.PI * 2;
                    const x1 = centerX + Math.cos(angle1) * radius * 0.5;
                    const y1 = centerY + Math.sin(angle1) * radius * 0.5;
                    
                    for (let j = i + 1; j < circles; j++) {
                        const angle2 = (j / circles) * Math.PI * 2;
                        const x2 = centerX + Math.cos(angle2) * radius * 0.5;
                        const y2 = centerY + Math.sin(angle2) * radius * 0.5;
                        
                        // Only draw connections between adjacent sacred points
                        if (Math.abs(i - j) <= 2 || Math.abs(i - j) >= circles - 2) {
                            this.ctx.beginPath();
                            this.ctx.moveTo(x1, y1);
                            this.ctx.lineTo(x2, y2);
                            this.ctx.stroke();
                        }
                    }
                }
                
                this.ctx.restore();
            }
            
            animate() {
                this.time++;
                this.drawSacredGeometry();
                requestAnimationFrame(() => this.animate());
            }
        }
        
        // Initialize the visualizer
        const visualizer = new SacredGeometryVisualizer();
    </script>
</body>
</html>
