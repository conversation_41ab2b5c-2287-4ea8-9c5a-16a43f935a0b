<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ultimate Dubstep Visualizer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(45deg, #0a0a0a, #1a0a2e, #2e0a1a);
            overflow: hidden;
            font-family: 'Courier New', monospace;
            color: #00ffff;
        }

        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #canvas {
            display: block;
            background: radial-gradient(circle at center, #001122, #000000);
        }

        #controls {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: rgba(0, 10, 20, 0.95);
            padding: 25px;
            border-radius: 20px;
            backdrop-filter: blur(15px);
            border: 2px solid rgba(0, 255, 255, 0.3);
            min-width: 300px;
            box-shadow: 0 0 30px rgba(0, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        #controls:hover {
            border-color: rgba(0, 255, 255, 0.6);
            box-shadow: 0 0 40px rgba(0, 255, 255, 0.4);
        }

        .control-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-size: 11px;
            text-transform: uppercase;
            letter-spacing: 2px;
            color: #00ccff;
            text-shadow: 0 0 5px rgba(0, 204, 255, 0.5);
        }

        input[type="file"] {
            width: 100%;
            padding: 12px;
            background: rgba(0, 30, 60, 0.4);
            border: 1px solid rgba(0, 255, 255, 0.5);
            border-radius: 8px;
            color: #00ffff;
            font-size: 12px;
            font-family: 'Courier New', monospace;
        }

        input[type="range"] {
            width: 100%;
            margin: 8px 0;
            -webkit-appearance: none;
            appearance: none;
            height: 8px;
            background: linear-gradient(to right, #001122, #003366);
            border-radius: 4px;
            outline: none;
            border: 1px solid rgba(0, 255, 255, 0.3);
        }

        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            background: linear-gradient(45deg, #00ffff, #0099cc);
            border-radius: 50%;
            cursor: pointer;
            border: 3px solid #006699;
            box-shadow: 0 0 15px rgba(0, 255, 255, 0.7);
        }

        input[type="range"]::-webkit-slider-thumb:hover {
            background: linear-gradient(45deg, #00ffff, #00ccff);
            box-shadow: 0 0 20px rgba(0, 255, 255, 1);
        }

        .value-display {
            font-size: 10px;
            color: #00ffaa;
            text-align: right;
            text-shadow: 0 0 3px rgba(0, 255, 170, 0.5);
        }

        button {
            width: 100%;
            background: linear-gradient(45deg, #003366, #006699);
            border: 2px solid #00aacc;
            padding: 12px 20px;
            border-radius: 8px;
            color: #00ffff;
            cursor: pointer;
            font-size: 12px;
            font-family: 'Courier New', monospace;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin: 8px 0;
            transition: all 0.3s ease;
            text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
        }

        button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 255, 255, 0.5);
            background: linear-gradient(45deg, #006699, #0099cc);
            border-color: #00ffff;
        }

        button:active {
            transform: translateY(-1px);
        }

        select {
            width: 100%;
            padding: 10px;
            background: rgba(0, 30, 60, 0.4);
            border: 1px solid rgba(0, 255, 255, 0.5);
            border-radius: 8px;
            color: #00ffff;
            font-size: 12px;
            font-family: 'Courier New', monospace;
        }

        #info {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: rgba(0, 255, 255, 0.8);
            font-size: 11px;
            max-width: 350px;
            text-shadow: 0 0 5px rgba(0, 255, 255, 0.3);
            line-height: 1.4;
        }

        #fps-counter {
            position: absolute;
            top: 20px;
            right: 20px;
            color: #00ff00;
            font-size: 12px;
            font-family: 'Courier New', monospace;
            text-shadow: 0 0 5px rgba(0, 255, 0, 0.5);
        }

        .preset-button {
            width: 48%;
            margin: 1%;
            display: inline-block;
        }

        .glow-effect {
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 0 0 5px rgba(0, 255, 255, 0.5); }
            to { text-shadow: 0 0 20px rgba(0, 255, 255, 1), 0 0 30px rgba(0, 255, 255, 0.8); }
        }

        .shake {
            animation: shake 0.1s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-2px); }
            75% { transform: translateX(2px); }
        }
    </style>
</head>
<body>
    <div id="container">
        <canvas id="canvas"></canvas>
        
        <div id="controls">
            <div class="control-group">
                <label>🎵 Audio File</label>
                <input type="file" id="fileInput" accept="audio/*">
                <button id="playPause">▶ Play/Pause</button>
            </div>
            
            <div class="control-group">
                <label>🔊 Bass Sensitivity</label>
                <input type="range" id="bassSensitivity" min="0.5" max="5" step="0.1" value="2">
                <div class="value-display" id="bassSensitivityValue">2.0</div>
            </div>
            
            <div class="control-group">
                <label>⚡ Drop Intensity</label>
                <input type="range" id="dropIntensity" min="1" max="10" step="0.5" value="5">
                <div class="value-display" id="dropIntensityValue">5.0</div>
            </div>

            <div class="control-group">
                <label>🔵 Ball Height</label>
                <input type="range" id="ballHeight" min="5" max="30" step="1" value="16">
                <div class="value-display" id="ballHeightValue">16</div>
            </div>

            <div class="control-group">
                <label>🌟 Particle Count</label>
                <input type="range" id="particleCount" min="500" max="5000" step="250" value="2000">
                <div class="value-display" id="particleCountValue">2000</div>
            </div>

            <div class="control-group">
                <label>💫 Beat Ring Sensitivity</label>
                <input type="range" id="beatSensitivity" min="0.3" max="1.0" step="0.05" value="0.7">
                <div class="value-display" id="beatSensitivityValue">0.7</div>
            </div>
            
            <div class="control-group">
                <label>🎨 Visual Mode</label>
                <select id="visualMode">
                    <option value="dubstep">Dubstep Mode</option>
                    <option value="bass">Bass Focus</option>
                    <option value="glitch">Glitch Mode</option>
                    <option value="cosmic">Cosmic Mode</option>
                </select>
            </div>

            <div class="control-group">
                <label>⚡ Performance</label>
                <select id="performanceMode">
                    <option value="normal">Normal Quality</option>
                    <option value="reduced">Reduced Quality</option>
                    <option value="minimal">Minimal (Safe)</option>
                </select>
            </div>
            
            <div class="control-group">
                <label>🎥 Camera Mode</label>
                <button id="toggleOrbit">AUTO ORBIT: ON</button>
            </div>



            <div class="control-group">
                <button class="preset-button" id="presetChill">CHILL</button>
            </div>
        </div>
        
        <div id="fps-counter">FPS: <span id="fps">60</span></div>
        
        <div id="info">
            <div class="glow-effect">🎧 ULTIMATE DUBSTEP VISUALIZER 🎧</div>
            <br>
            • Advanced bass detection & wobble visualization<br>
            • Drop detection with screen shake effects<br>
            • 7 rings that bounce up and down to the music<br>
            • 3 beat-reactive rings that expand from main rings<br>
            • Auto-orbiting camera synced to music<br>
            • 3D particle systems with physics<br>
            • Mouse: Manual camera control • Scroll: Zoom<br>
            • Auto orbit resumes after 3 seconds of no interaction<br>
            • Optimized for dubstep, trap, and electronic music
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script>
        class UltimateDubstepVisualizer {
            constructor() {
                // Core Three.js components
                this.scene = null;
                this.camera = null;
                this.renderer = null;

                // Audio components
                this.audioContext = null;
                this.analyser = null;
                this.audio = null;
                this.dataArray = null;
                this.bassAnalyser = null;
                this.midAnalyser = null;
                this.highAnalyser = null;
                this.bassDataArray = null;
                this.midDataArray = null;
                this.highDataArray = null;

                // Visualization parameters
                this.bassSensitivity = 2.0;
                this.dropIntensity = 5.0;
                this.ballHeight = 16;
                this.particleCount = 2000;
                this.visualMode = 'dubstep';

                // Performance tracking
                this.frameCount = 0;
                this.lastTime = performance.now();
                this.fps = 60;
                this.fpsUpdateInterval = 30;
                this.updateFrequency = 2; // Update every 2 frames for better performance

                // Audio analysis
                this.bassLevel = 0;
                this.midLevel = 0;
                this.highLevel = 0;
                this.previousBassLevel = 0;
                this.dropDetected = false;
                this.dropCooldown = 0;
                this.beatHistory = [];

                // Visual elements
                this.particles = null;
                this.bassGeometry = null;
                this.wobbleElements = [];
                this.lightningEffects = [];
                this.shockwaves = [];

                // Beat-reactive rings that come off the main ring system
                this.beatRings = [];
                this.beatRingPool = []; // Pool of rings for reuse
                this.lastBeatTime = 0;
                this.beatThreshold = 0.7; // Threshold for beat detection

                // Camera controls
                this.mouseX = 0;
                this.mouseY = 0;
                this.isMouseDown = false;
                this.cameraRotationX = 0.2;
                this.cameraRotationY = 0;
                this.cameraDistance = 25;
                this.cameraShake = { x: 0, y: 0, intensity: 0 };

                // Enhanced auto camera orbit system
                this.autoOrbit = true;
                this.orbitSpeed = 0.008;
                this.orbitRadius = 25;
                this.orbitHeight = 0.2;
                this.manualControl = false;

                // Cinematic camera modes
                this.cameraMode = 'dynamic'; // dynamic, closeup, topdown, lowangle, sweep
                this.cameraModeTimer = 0;
                this.cameraModeInterval = 300; // Change mode every 5 seconds (300 frames at 60fps)
                this.cameraTransition = { active: false, progress: 0, duration: 60 };

                // Camera presets for different modes
                this.cameraPresets = {
                    dynamic: { distance: 25, height: 0.2, speed: 0.008 },
                    closeup: { distance: 12, height: 0.1, speed: 0.015 },
                    topdown: { distance: 30, height: 1.4, speed: 0.005 },
                    lowangle: { distance: 35, height: -0.8, speed: 0.006 },
                    sweep: { distance: 40, height: 0.0, speed: 0.012 }
                };

                // State
                this.isPlaying = false;
                this.isInitialized = false;
                this.performanceMode = 'normal'; // normal, reduced, minimal

                this.init();
                this.setupEventListeners();
                this.animate();
            }

            init() {
                console.log('🎵 Initializing Ultimate Dubstep Visualizer...');

                const canvas = document.getElementById('canvas');

                // Scene setup
                this.scene = new THREE.Scene();
                this.scene.fog = new THREE.Fog(0x001122, 30, 150);

                // Camera setup
                this.camera = new THREE.PerspectiveCamera(
                    75,
                    window.innerWidth / window.innerHeight,
                    0.1,
                    1000
                );
                this.updateCameraPosition();

                // Renderer setup with advanced settings
                this.renderer = new THREE.WebGLRenderer({
                    canvas: canvas,
                    antialias: true,
                    alpha: true,
                    powerPreference: "high-performance"
                });
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.setClearColor(0x000000, 1);
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));

                this.setupLighting();
                this.createParticleSystem();
                this.createBassGeometry();
                this.createWobbleElements();
                this.createBeatRingPool();

                this.isInitialized = true;
                console.log('✅ Visualizer initialized successfully!');
            }

            setupLighting() {
                // Dynamic ambient light
                this.ambientLight = new THREE.AmbientLight(0x001133, 0.3);
                this.scene.add(this.ambientLight);

                // Main directional light
                this.mainLight = new THREE.DirectionalLight(0x00ffff, 1.2);
                this.mainLight.position.set(20, 30, 20);
                this.mainLight.castShadow = true;
                this.mainLight.shadow.mapSize.width = 2048;
                this.mainLight.shadow.mapSize.height = 2048;
                this.scene.add(this.mainLight);

                // Bass-reactive point lights
                this.bassLight1 = new THREE.PointLight(0xff0080, 2, 100);
                this.bassLight1.position.set(15, 10, 15);
                this.scene.add(this.bassLight1);

                this.bassLight2 = new THREE.PointLight(0x0080ff, 2, 100);
                this.bassLight2.position.set(-15, 10, -15);
                this.scene.add(this.bassLight2);

                // High-frequency reactive light
                this.highLight = new THREE.PointLight(0x00ff80, 1.5, 80);
                this.highLight.position.set(0, 20, -25);
                this.scene.add(this.highLight);

                // Strobe light for drops
                this.strobeLight = new THREE.PointLight(0xffffff, 0, 200);
                this.strobeLight.position.set(0, 50, 0);
                this.scene.add(this.strobeLight);
            }

            setupAudio(file) {
                if (this.audio) {
                    this.audio.pause();
                }

                this.audio = new Audio();
                this.audio.src = URL.createObjectURL(file);
                this.audio.crossOrigin = "anonymous";

                if (!this.audioContext) {
                    this.audioContext = new (window.AudioContext || window.webkitAudioContext)();

                    // Main analyser for overall frequency data
                    this.analyser = this.audioContext.createAnalyser();
                    this.analyser.fftSize = 2048;
                    this.dataArray = new Uint8Array(this.analyser.frequencyBinCount);

                    // Bass analyser (20Hz - 250Hz)
                    this.bassAnalyser = this.audioContext.createAnalyser();
                    this.bassAnalyser.fftSize = 512;
                    this.bassDataArray = new Uint8Array(this.bassAnalyser.frequencyBinCount);

                    // Mid analyser (250Hz - 4kHz)
                    this.midAnalyser = this.audioContext.createAnalyser();
                    this.midAnalyser.fftSize = 1024;
                    this.midDataArray = new Uint8Array(this.midAnalyser.frequencyBinCount);

                    // High analyser (4kHz - 20kHz)
                    this.highAnalyser = this.audioContext.createAnalyser();
                    this.highAnalyser.fftSize = 1024;
                    this.highDataArray = new Uint8Array(this.highAnalyser.frequencyBinCount);
                }

                const source = this.audioContext.createMediaElementSource(this.audio);

                // Create filters for frequency separation
                const bassFilter = this.audioContext.createBiquadFilter();
                bassFilter.type = 'lowpass';
                bassFilter.frequency.setValueAtTime(250, this.audioContext.currentTime);

                const midFilter = this.audioContext.createBiquadFilter();
                midFilter.type = 'bandpass';
                midFilter.frequency.setValueAtTime(1000, this.audioContext.currentTime);
                midFilter.Q.setValueAtTime(0.5, this.audioContext.currentTime);

                const highFilter = this.audioContext.createBiquadFilter();
                highFilter.type = 'highpass';
                highFilter.frequency.setValueAtTime(4000, this.audioContext.currentTime);

                // Connect audio graph
                source.connect(this.analyser);
                source.connect(bassFilter);
                source.connect(midFilter);
                source.connect(highFilter);

                bassFilter.connect(this.bassAnalyser);
                midFilter.connect(this.midAnalyser);
                highFilter.connect(this.highAnalyser);

                this.analyser.connect(this.audioContext.destination);

                this.audio.addEventListener('loadeddata', () => {
                    console.log('🎵 Audio loaded successfully');
                });
            }

            createParticleSystem() {
                // Limit particle count for better performance
                const maxParticles = Math.min(this.particleCount, 2000);

                const particleGeometry = new THREE.BufferGeometry();
                const particlePositions = new Float32Array(maxParticles * 3);
                const particleVelocities = new Float32Array(maxParticles * 3);
                const particleColors = new Float32Array(maxParticles * 3);

                // Initialize particles
                for (let i = 0; i < maxParticles; i++) {
                    const i3 = i * 3;

                    // Random positions in a sphere
                    const radius = Math.random() * 30 + 10;
                    const theta = Math.random() * Math.PI * 2;
                    const phi = Math.random() * Math.PI;

                    particlePositions[i3] = radius * Math.sin(phi) * Math.cos(theta);
                    particlePositions[i3 + 1] = radius * Math.cos(phi);
                    particlePositions[i3 + 2] = radius * Math.sin(phi) * Math.sin(theta);

                    // Random velocities
                    particleVelocities[i3] = (Math.random() - 0.5) * 0.05;
                    particleVelocities[i3 + 1] = (Math.random() - 0.5) * 0.05;
                    particleVelocities[i3 + 2] = (Math.random() - 0.5) * 0.05;

                    // Cyan-based colors
                    particleColors[i3] = 0.0;
                    particleColors[i3 + 1] = Math.random() * 0.5 + 0.5;
                    particleColors[i3 + 2] = Math.random() * 0.5 + 0.5;
                }

                particleGeometry.setAttribute('position', new THREE.BufferAttribute(particlePositions, 3));
                particleGeometry.setAttribute('velocity', new THREE.BufferAttribute(particleVelocities, 3));
                particleGeometry.setAttribute('color', new THREE.BufferAttribute(particleColors, 3));

                const particleMaterial = new THREE.PointsMaterial({
                    size: 3,
                    vertexColors: true,
                    transparent: true,
                    opacity: 0.7,
                    blending: THREE.AdditiveBlending,
                    sizeAttenuation: false // Better performance
                });

                this.particles = new THREE.Points(particleGeometry, particleMaterial);
                this.actualParticleCount = maxParticles;
                this.scene.add(this.particles);
            }

            createBassGeometry() {
                // Create environment map for reflections
                const cubeRenderTarget = new THREE.WebGLCubeRenderTarget(256);
                const cubeCamera = new THREE.CubeCamera(0.1, 1000, cubeRenderTarget);
                this.scene.add(cubeCamera);
                this.cubeCamera = cubeCamera;

                // Central bass-reactive sphere with reflective material
                const sphereGeometry = new THREE.SphereGeometry(3, 64, 64);
                const sphereMaterial = new THREE.MeshStandardMaterial({
                    color: 0x00ffff,
                    metalness: 0.9,
                    roughness: 0.1,
                    emissive: 0x002222,
                    emissiveIntensity: 0.3,
                    envMap: cubeRenderTarget.texture,
                    envMapIntensity: 1.0
                });

                this.bassSphere = new THREE.Mesh(sphereGeometry, sphereMaterial);
                this.bassSphere.position.set(0, 0, 0);
                this.scene.add(this.bassSphere);

                // Enhanced Bass rings with individual properties
                this.bassRings = [];
                for (let i = 0; i < 7; i++) {
                    const ringGeometry = new THREE.TorusGeometry(6 + i * 2.5, 0.02, 8, 32);
                    const ringMaterial = new THREE.MeshPhongMaterial({
                        color: new THREE.Color().setHSL(0.5 + i * 0.12, 1, 0.6),
                        transparent: true,
                        opacity: 0.8,
                        emissive: new THREE.Color().setHSL(0.5 + i * 0.12, 1, 0.2),
                        shininess: 100
                    });

                    const ring = new THREE.Mesh(ringGeometry, ringMaterial);
                    ring.rotation.x = Math.PI / 2;
                    ring.position.y = i * 1.5 - 4.5;

                    // Store individual ring properties for up/down animation
                    const ringData = {
                        mesh: ring,
                        baseY: ring.position.y,
                        baseScale: 1,
                        frequencyRange: i, // Each ring responds to different frequency range
                        pulsePhase: i * Math.PI / 3,
                        colorHue: 0.5 + i * 0.12,
                        material: ringMaterial
                    };

                    this.bassRings.push(ringData);
                    this.scene.add(ring);
                }

                // Frequency bars around the sphere
                this.frequencyBars = [];
                const barCount = 64;
                for (let i = 0; i < barCount; i++) {
                    const barGeometry = new THREE.BoxGeometry(0.3, 1, 0.3);
                    const barMaterial = new THREE.MeshPhongMaterial({
                        color: new THREE.Color().setHSL(i / barCount, 1, 0.5),
                        transparent: true,
                        opacity: 0.8,
                        emissive: new THREE.Color().setHSL(i / barCount, 1, 0.1)
                    });

                    const bar = new THREE.Mesh(barGeometry, barMaterial);
                    const angle = (i / barCount) * Math.PI * 2;
                    const radius = 15;

                    bar.position.x = Math.cos(angle) * radius;
                    bar.position.z = Math.sin(angle) * radius;
                    bar.position.y = 0;

                    this.frequencyBars.push({
                        mesh: bar,
                        baseScale: 1,
                        targetScale: 1,
                        angle: angle
                    });

                    this.scene.add(bar);
                }
            }

            createWobbleElements() {
                // Create wobble effect geometry with reduced complexity
                this.wobbleGeometry = new THREE.PlaneGeometry(80, 80, 25, 25);
                this.wobbleMaterial = new THREE.MeshPhongMaterial({
                    color: 0x004466,
                    transparent: true,
                    opacity: 0.2,
                    wireframe: true,
                    side: THREE.DoubleSide
                });

                this.wobblePlane = new THREE.Mesh(this.wobbleGeometry, this.wobbleMaterial);
                this.wobblePlane.rotation.x = -Math.PI / 2;
                this.wobblePlane.position.y = -10;
                this.scene.add(this.wobblePlane);

                // Store original positions for wobble effect
                this.originalPositions = this.wobbleGeometry.attributes.position.array.slice();
            }

            createBeatRingPool() {
                // Create a pool of 3 rings that will be triggered by beats
                for (let i = 0; i < 3; i++) {
                    const ringGeometry = new THREE.TorusGeometry(8, 0.15, 8, 32);
                    const ringMaterial = new THREE.MeshPhongMaterial({
                        color: new THREE.Color().setHSL(0.15 + i * 0.25, 1, 0.7),
                        transparent: true,
                        opacity: 0,
                        emissive: new THREE.Color().setHSL(0.15 + i * 0.25, 1, 0.3),
                        shininess: 100
                    });

                    const ring = new THREE.Mesh(ringGeometry, ringMaterial);
                    ring.rotation.x = Math.PI / 2;
                    ring.position.set(0, 0, 0);
                    ring.scale.setScalar(0.5);

                    const ringData = {
                        mesh: ring,
                        active: false,
                        startTime: 0,
                        duration: 2000, // 2 seconds animation
                        startRadius: 8,
                        endRadius: 25,
                        startOpacity: 0.8,
                        colorHue: 0.15 + i * 0.25,
                        material: ringMaterial,
                        sourceRingIndex: 0 // Which main ring this came from
                    };

                    this.beatRingPool.push(ringData);
                    this.scene.add(ring);
                }
            }

            togglePlayPause() {
                if (!this.audio) return;

                if (this.isPlaying) {
                    this.audio.pause();
                    this.isPlaying = false;
                } else {
                    if (this.audioContext.state === 'suspended') {
                        this.audioContext.resume();
                    }
                    this.audio.play();
                    this.isPlaying = true;
                }
            }

            updateCameraPosition() {
                // Auto orbit camera if enabled and not manually controlled
                if (this.autoOrbit && !this.manualControl) {
                    this.updateCinematicCamera();
                }

                const x = Math.cos(this.cameraRotationY) * Math.cos(this.cameraRotationX) * this.cameraDistance;
                const y = Math.sin(this.cameraRotationX) * this.cameraDistance;
                const z = Math.sin(this.cameraRotationY) * Math.cos(this.cameraRotationX) * this.cameraDistance;

                // Apply camera shake
                this.camera.position.set(
                    x + this.cameraShake.x,
                    y + this.cameraShake.y,
                    z
                );

                // Dynamic look-at target based on bass level
                const lookAtY = this.bassLevel * this.bassSensitivity * this.ballHeight * 0.5;
                this.camera.lookAt(0, lookAtY, 0);
            }

            updateCinematicCamera() {
                // Camera mode switching based on music intensity
                this.cameraModeTimer++;

                // Switch modes based on music or timer
                if (this.cameraModeTimer >= this.cameraModeInterval || this.dropDetected) {
                    this.switchCameraMode();
                    this.cameraModeTimer = 0;
                }

                // Get current preset
                const preset = this.cameraPresets[this.cameraMode];

                // Apply mode-specific camera behavior
                switch(this.cameraMode) {
                    case 'dynamic':
                        this.updateDynamicCamera(preset);
                        break;
                    case 'closeup':
                        this.updateCloseupCamera(preset);
                        break;
                    case 'topdown':
                        this.updateTopdownCamera(preset);
                        break;
                    case 'lowangle':
                        this.updateLowAngleCamera(preset);
                        break;
                    case 'sweep':
                        this.updateSweepCamera(preset);
                        break;
                }
            }

            switchCameraMode() {
                const modes = ['dynamic', 'closeup', 'topdown', 'lowangle', 'sweep'];

                // Choose mode based on music intensity
                let newMode;
                if (this.dropDetected) {
                    // During drops, prefer dramatic angles
                    newMode = Math.random() < 0.5 ? 'closeup' : 'lowangle';
                } else if (this.bassLevel > 0.7) {
                    // High energy - dynamic or sweep
                    newMode = Math.random() < 0.6 ? 'dynamic' : 'sweep';
                } else if (this.bassLevel < 0.3) {
                    // Low energy - topdown or dynamic
                    newMode = Math.random() < 0.4 ? 'topdown' : 'dynamic';
                } else {
                    // Random selection
                    newMode = modes[Math.floor(Math.random() * modes.length)];
                }

                if (newMode !== this.cameraMode) {
                    console.log(`🎥 Camera mode: ${this.cameraMode} → ${newMode}`);
                    this.cameraMode = newMode;
                    this.startCameraTransition();
                }
            }

            startCameraTransition() {
                this.cameraTransition.active = true;
                this.cameraTransition.progress = 0;
            }

            updateDynamicCamera(preset) {
                // Standard orbiting with music-reactive variations
                const musicOrbitSpeed = preset.speed + (this.bassLevel * 0.015);
                this.cameraRotationY += musicOrbitSpeed;

                // Vertical movement based on mid frequencies
                const verticalMovement = Math.sin(this.frameCount * 0.008) * this.midLevel * 0.4;
                this.cameraRotationX = preset.height + verticalMovement;

                // Distance varies with high frequencies
                this.cameraDistance = preset.distance + (this.highLevel * 10);
            }

            updateCloseupCamera(preset) {
                // Close, intimate shots with faster movement
                const musicOrbitSpeed = preset.speed + (this.bassLevel * 0.02);
                this.cameraRotationY += musicOrbitSpeed;

                // More dramatic vertical movement
                const verticalMovement = Math.sin(this.frameCount * 0.012) * (this.midLevel + 0.3) * 0.6;
                this.cameraRotationX = preset.height + verticalMovement;

                // Close distance with bass-reactive zoom
                this.cameraDistance = preset.distance + (this.bassLevel * 6);
            }

            updateTopdownCamera(preset) {
                // High angle, looking down
                const musicOrbitSpeed = preset.speed + (this.bassLevel * 0.008);
                this.cameraRotationY += musicOrbitSpeed;

                // Stay mostly above, slight variation
                const verticalMovement = Math.sin(this.frameCount * 0.005) * this.midLevel * 0.2;
                this.cameraRotationX = preset.height + verticalMovement;

                // Moderate distance
                this.cameraDistance = preset.distance + (this.highLevel * 8);
            }

            updateLowAngleCamera(preset) {
                // Low angle, looking up - dramatic
                const musicOrbitSpeed = preset.speed + (this.bassLevel * 0.01);
                this.cameraRotationY += musicOrbitSpeed;

                // Stay low with slight movement
                const verticalMovement = Math.sin(this.frameCount * 0.006) * this.midLevel * 0.3;
                this.cameraRotationX = preset.height + verticalMovement;

                // Further distance for dramatic effect
                this.cameraDistance = preset.distance + (this.highLevel * 12);
            }

            updateSweepCamera(preset) {
                // Fast sweeping movements
                const musicOrbitSpeed = preset.speed + (this.bassLevel * 0.025);
                this.cameraRotationY += musicOrbitSpeed;

                // Sine wave vertical movement
                const verticalMovement = Math.sin(this.frameCount * 0.015) * (this.midLevel + 0.2) * 0.8;
                this.cameraRotationX = preset.height + verticalMovement;

                // Variable distance
                this.cameraDistance = preset.distance + Math.sin(this.frameCount * 0.01) * 15 + (this.highLevel * 10);
            }

            analyzeAudio() {
                if (!this.analyser || !this.isPlaying) return;

                // Get frequency data
                this.analyser.getByteFrequencyData(this.dataArray);
                this.bassAnalyser.getByteFrequencyData(this.bassDataArray);
                this.midAnalyser.getByteFrequencyData(this.midDataArray);
                this.highAnalyser.getByteFrequencyData(this.highDataArray);

                // Calculate average levels
                this.previousBassLevel = this.bassLevel;
                this.bassLevel = this.getAverageFrequency(this.bassDataArray) / 255;
                this.midLevel = this.getAverageFrequency(this.midDataArray) / 255;
                this.highLevel = this.getAverageFrequency(this.highDataArray) / 255;

                // Drop detection
                this.detectDrop();

                // Beat detection for ring effects
                this.detectBeat();

                // Update beat history for rhythm analysis
                this.beatHistory.push(this.bassLevel);
                if (this.beatHistory.length > 60) {
                    this.beatHistory.shift();
                }
            }

            getAverageFrequency(dataArray) {
                let sum = 0;
                for (let i = 0; i < dataArray.length; i++) {
                    sum += dataArray[i];
                }
                return sum / dataArray.length;
            }

            detectDrop() {
                if (this.dropCooldown > 0) {
                    this.dropCooldown--;
                    return;
                }

                // Detect sudden bass increase (drop)
                const bassIncrease = this.bassLevel - this.previousBassLevel;
                const threshold = 0.3;

                if (bassIncrease > threshold && this.bassLevel > 0.6) {
                    this.dropDetected = true;
                    this.dropCooldown = 60; // Prevent multiple detections
                    this.triggerDropEffects();
                    console.log('🔥 DROP DETECTED!');
                } else {
                    this.dropDetected = false;
                }
            }

            detectBeat() {
                const currentTime = performance.now();

                // Beat detection based on bass level and timing
                if (this.bassLevel > this.beatThreshold &&
                    currentTime - this.lastBeatTime > 200) { // Minimum 200ms between beats

                    this.lastBeatTime = currentTime;
                    this.triggerBeatRing();
                    return true;
                }
                return false;
            }

            triggerBeatRing() {
                // Find an inactive ring from the pool
                const availableRing = this.beatRingPool.find(ring => !ring.active);
                if (!availableRing) return;

                // Choose a random source ring from the main bass rings
                const sourceRingIndex = Math.floor(Math.random() * this.bassRings.length);
                const sourceRing = this.bassRings[sourceRingIndex];

                // Position the beat ring at the source ring's position
                availableRing.mesh.position.copy(sourceRing.mesh.position);
                availableRing.mesh.rotation.copy(sourceRing.mesh.rotation);

                // Set initial properties
                availableRing.active = true;
                availableRing.startTime = performance.now();
                availableRing.sourceRingIndex = sourceRingIndex;
                availableRing.mesh.scale.setScalar(sourceRing.mesh.scale.x * 0.8);

                // Set initial color based on source ring
                const sourceColor = sourceRing.material.color.getHSL({});
                availableRing.material.color.setHSL(sourceColor.h, sourceColor.s, sourceColor.l);
                availableRing.material.emissive.setHSL(sourceColor.h, sourceColor.s, sourceColor.l * 0.5);
                availableRing.material.opacity = availableRing.startOpacity;

                console.log(`🎵 Beat ring triggered from ring ${sourceRingIndex}`);
            }

            triggerDropEffects() {
                // Screen shake
                this.cameraShake.intensity = this.dropIntensity;

                // Strobe light
                this.strobeLight.intensity = 10;

                // Particle explosion
                this.explodeParticles();

                // UI shake effect
                document.getElementById('controls').classList.add('shake');
                setTimeout(() => {
                    document.getElementById('controls').classList.remove('shake');
                }, 100);
            }

            explodeParticles() {
                if (!this.particles) return;

                const velocities = this.particles.geometry.attributes.velocity.array;
                const particleCount = this.actualParticleCount || this.particleCount;
                const explosionForce = this.dropIntensity * 0.1; // Reduced force

                // Only affect a subset of particles for better performance
                const affectedCount = Math.min(particleCount, 500);

                for (let i = 0; i < affectedCount; i++) {
                    const i3 = i * 3;

                    velocities[i3] += (Math.random() - 0.5) * explosionForce;
                    velocities[i3 + 1] += (Math.random() - 0.5) * explosionForce;
                    velocities[i3 + 2] += (Math.random() - 0.5) * explosionForce;
                }

                this.particles.geometry.attributes.velocity.needsUpdate = true;
            }

            setupEventListeners() {
                // File input
                document.getElementById('fileInput').addEventListener('change', (e) => {
                    if (e.target.files.length > 0) {
                        this.setupAudio(e.target.files[0]);
                    }
                });

                // Play/Pause button
                document.getElementById('playPause').addEventListener('click', () => {
                    this.togglePlayPause();
                });

                // Control sliders
                document.getElementById('bassSensitivity').addEventListener('input', (e) => {
                    this.bassSensitivity = parseFloat(e.target.value);
                    document.getElementById('bassSensitivityValue').textContent = this.bassSensitivity.toFixed(1);
                });

                document.getElementById('dropIntensity').addEventListener('input', (e) => {
                    this.dropIntensity = parseFloat(e.target.value);
                    document.getElementById('dropIntensityValue').textContent = this.dropIntensity.toFixed(1);
                });

                document.getElementById('ballHeight').addEventListener('input', (e) => {
                    this.ballHeight = parseFloat(e.target.value);
                    document.getElementById('ballHeightValue').textContent = this.ballHeight.toFixed(0);
                });

                document.getElementById('particleCount').addEventListener('input', (e) => {
                    this.particleCount = parseInt(e.target.value);
                    document.getElementById('particleCountValue').textContent = this.particleCount;
                    this.recreateParticles();
                });

                document.getElementById('visualMode').addEventListener('change', (e) => {
                    this.visualMode = e.target.value;
                    this.applyVisualMode();
                });

                document.getElementById('performanceMode').addEventListener('change', (e) => {
                    this.performanceMode = e.target.value;
                    this.applyPerformanceMode();
                });

                // Preset button
                document.getElementById('presetChill').addEventListener('click', () => {
                    this.applyPreset('chill');
                });

                // Camera orbit toggle
                document.getElementById('toggleOrbit').addEventListener('click', () => {
                    this.autoOrbit = !this.autoOrbit;
                    document.getElementById('toggleOrbit').textContent =
                        `AUTO ORBIT: ${this.autoOrbit ? 'ON' : 'OFF'}`;

                    if (!this.autoOrbit) {
                        this.manualControl = false;
                    }
                });



                // Mouse controls
                const canvas = document.getElementById('canvas');

                canvas.addEventListener('mousedown', (e) => {
                    this.isMouseDown = true;
                    this.mouseX = e.clientX;
                    this.mouseY = e.clientY;
                    this.manualControl = true; // Enable manual control when user interacts
                });

                canvas.addEventListener('mousemove', (e) => {
                    if (this.isMouseDown) {
                        this.manualControl = true;
                        const deltaX = e.clientX - this.mouseX;
                        const deltaY = e.clientY - this.mouseY;

                        this.cameraRotationY += deltaX * 0.01;
                        this.cameraRotationX -= deltaY * 0.01;
                        this.cameraRotationX = Math.max(-Math.PI/2, Math.min(Math.PI/2, this.cameraRotationX));

                        this.updateCameraPosition();

                        this.mouseX = e.clientX;
                        this.mouseY = e.clientY;
                    }
                });

                canvas.addEventListener('mouseup', () => {
                    this.isMouseDown = false;
                    // Resume auto orbit after 3 seconds of no interaction
                    if (this.autoOrbit) {
                        setTimeout(() => {
                            if (!this.isMouseDown) {
                                this.manualControl = false;
                            }
                        }, 3000);
                    }
                });

                canvas.addEventListener('wheel', (e) => {
                    e.preventDefault();
                    this.manualControl = true;
                    this.cameraDistance += e.deltaY * 0.02;
                    this.cameraDistance = Math.max(10, Math.min(100, this.cameraDistance));
                    this.orbitRadius = this.cameraDistance; // Update orbit radius
                    this.updateCameraPosition();

                    // Resume auto orbit after 2 seconds of no scrolling
                    if (this.autoOrbit) {
                        setTimeout(() => {
                            if (!this.isMouseDown) {
                                this.manualControl = false;
                            }
                        }, 2000);
                    }
                });

                // Window resize
                window.addEventListener('resize', () => {
                    this.camera.aspect = window.innerWidth / window.innerHeight;
                    this.camera.updateProjectionMatrix();
                    this.renderer.setSize(window.innerWidth, window.innerHeight);
                });
            }

            applyPreset(preset) {
                if (preset === 'chill') {
                    this.bassSensitivity = 1.0;
                    this.dropIntensity = 2.0;
                    this.particleCount = 1000;

                    // Update UI
                    document.getElementById('bassSensitivity').value = this.bassSensitivity;
                    document.getElementById('bassSensitivityValue').textContent = this.bassSensitivity.toFixed(1);
                    document.getElementById('dropIntensity').value = this.dropIntensity;
                    document.getElementById('dropIntensityValue').textContent = this.dropIntensity.toFixed(1);
                    document.getElementById('particleCount').value = this.particleCount;
                    document.getElementById('particleCountValue').textContent = this.particleCount;

                    this.recreateParticles();
                }
            }

            applyVisualMode() {
                switch(this.visualMode) {
                    case 'dubstep':
                        this.scene.fog.color.setHex(0x001122);
                        this.ambientLight.color.setHex(0x001133);
                        break;
                    case 'bass':
                        this.scene.fog.color.setHex(0x220011);
                        this.ambientLight.color.setHex(0x330011);
                        break;
                    case 'glitch':
                        this.scene.fog.color.setHex(0x112200);
                        this.ambientLight.color.setHex(0x113300);
                        break;
                    case 'cosmic':
                        this.scene.fog.color.setHex(0x110022);
                        this.ambientLight.color.setHex(0x110033);
                        break;
                }
            }

            applyPerformanceMode() {
                switch(this.performanceMode) {
                    case 'normal':
                        this.updateFrequency = 2;
                        this.particleCount = Math.min(this.particleCount, 2000);
                        break;
                    case 'reduced':
                        this.updateFrequency = 3;
                        this.particleCount = Math.min(this.particleCount, 1000);
                        break;
                    case 'minimal':
                        this.updateFrequency = 5;
                        this.particleCount = Math.min(this.particleCount, 500);
                        break;
                }

                // Update UI
                document.getElementById('particleCount').value = this.particleCount;
                document.getElementById('particleCountValue').textContent = this.particleCount;

                this.recreateParticles();
                console.log(`🔧 Performance mode: ${this.performanceMode}`);
            }



            recreateParticles() {
                if (this.particles) {
                    this.scene.remove(this.particles);
                }
                this.createParticleSystem();
            }







            updateVisualization() {
                if (!this.isInitialized) return;

                // Skip frames for better performance
                if (this.frameCount % this.updateFrequency !== 0) return;

                this.analyzeAudio();

                // Update particles (less frequently)
                if (this.frameCount % (this.updateFrequency * 2) === 0) {
                    this.updateParticles();
                }

                // Update bass geometry
                this.updateBassGeometry();

                // Update beat rings
                this.updateBeatRings();

                // Update wobble effect (less frequently)
                if (this.frameCount % (this.updateFrequency * 3) === 0) {
                    this.updateWobbleEffect();
                }

                // Update lighting
                this.updateLighting();

                // Update camera shake
                this.updateCameraShake();
            }

            updateParticles() {
                if (!this.particles) return;

                const positions = this.particles.geometry.attributes.position.array;
                const velocities = this.particles.geometry.attributes.velocity.array;
                const colors = this.particles.geometry.attributes.color.array;
                const particleCount = this.actualParticleCount || this.particleCount;

                // Pre-calculate values to avoid repeated calculations
                const bassInfluence = this.bassLevel * this.bassSensitivity * 0.01;
                const damping = 0.99;

                for (let i = 0; i < particleCount; i++) {
                    const i3 = i * 3;

                    // Update positions based on velocities
                    positions[i3] += velocities[i3];
                    positions[i3 + 1] += velocities[i3 + 1];
                    positions[i3 + 2] += velocities[i3 + 2];

                    // Apply damping and bass influence
                    velocities[i3] *= damping + bassInfluence;
                    velocities[i3 + 1] *= damping + bassInfluence;
                    velocities[i3 + 2] *= damping + bassInfluence;

                    // Update colors less frequently
                    if (i % 4 === 0) {
                        colors[i3] = this.bassLevel * 0.3; // Red
                        colors[i3 + 1] = this.midLevel * 0.6 + 0.4; // Green
                        colors[i3 + 2] = this.highLevel * 0.4 + 0.6; // Blue
                    }

                    // Reset particles that go too far (check less frequently)
                    if (i % 10 === 0) {
                        const distanceSquared = positions[i3]**2 + positions[i3+1]**2 + positions[i3+2]**2;
                        if (distanceSquared > 6400) { // 80^2
                            positions[i3] = (Math.random() - 0.5) * 15;
                            positions[i3 + 1] = (Math.random() - 0.5) * 15;
                            positions[i3 + 2] = (Math.random() - 0.5) * 15;
                            velocities[i3] = (Math.random() - 0.5) * 0.05;
                            velocities[i3 + 1] = (Math.random() - 0.5) * 0.05;
                            velocities[i3 + 2] = (Math.random() - 0.5) * 0.05;
                        }
                    }
                }

                this.particles.geometry.attributes.position.needsUpdate = true;
                this.particles.geometry.attributes.color.needsUpdate = true;
            }

            updateBassGeometry() {
                if (!this.bassSphere) return;

                // Update environment map for reflections (less frequently for performance)
                if (this.cubeCamera && this.frameCount % 5 === 0) {
                    this.bassSphere.visible = false; // Hide sphere while updating environment
                    this.cubeCamera.position.copy(this.bassSphere.position);
                    this.cubeCamera.update(this.renderer, this.scene);
                    this.bassSphere.visible = true;
                }

                // Scale bass sphere - more audio reactive
                const bassScale = 1 + this.bassLevel * this.bassSensitivity * 1.2;
                this.bassSphere.scale.setScalar(bassScale);

                // Move bass sphere - adjustable height
                const bassHeight = this.bassLevel * this.bassSensitivity * this.ballHeight;
                this.bassSphere.position.y = bassHeight;

                // Simple bass rings animation - just up and down movement
                this.bassRings.forEach((ringData, index) => {
                    const ring = ringData.mesh;
                    const time = this.frameCount * 0.01;

                    // Get frequency data for this ring's range
                    let frequencyValue = 0;
                    if (this.dataArray && this.dataArray.length > 0) {
                        const startIndex = Math.floor((index / this.bassRings.length) * this.dataArray.length * 0.3);
                        const endIndex = Math.floor(((index + 1) / this.bassRings.length) * this.dataArray.length * 0.3);
                        let sum = 0;
                        for (let i = startIndex; i < endIndex && i < this.dataArray.length; i++) {
                            sum += this.dataArray[i];
                        }
                        frequencyValue = sum / (endIndex - startIndex) / 255;
                    }

                    // Keep rings horizontal (no rotation)
                    ring.rotation.x = Math.PI / 2;
                    ring.rotation.y = 0;
                    ring.rotation.z = 0;

                    // Individual scaling based on frequency
                    const ringScale = ringData.baseScale + frequencyValue * this.bassSensitivity * 0.8;
                    ring.scale.setScalar(ringScale);

                    // Vertical movement only - bouncing to the music
                    const musicBounce = frequencyValue * this.bassSensitivity * 4;
                    const gentleFloat = Math.sin(time * 1.5 + ringData.pulsePhase) * 0.5;
                    ring.position.y = ringData.baseY + musicBounce + gentleFloat;

                    // Dynamic color changes
                    const colorShift = (time * 0.1 + index * 0.2) % 1;
                    const intensity = 0.4 + frequencyValue * 0.6;
                    ringData.material.color.setHSL(
                        (ringData.colorHue + colorShift) % 1,
                        1,
                        intensity
                    );
                    ringData.material.emissive.setHSL(
                        (ringData.colorHue + colorShift) % 1,
                        1,
                        intensity * 0.3
                    );

                    // Opacity pulsing
                    ringData.material.opacity = 0.6 + Math.sin(time * 3 + ringData.pulsePhase) * 0.2 + frequencyValue * 0.3;
                });

                // Update frequency bars
                if (this.dataArray) {
                    this.frequencyBars.forEach((barData, index) => {
                        const dataIndex = Math.floor((index / this.frequencyBars.length) * this.dataArray.length);
                        const frequency = this.dataArray[dataIndex] / 255;

                        barData.targetScale = 1 + frequency * this.bassSensitivity * 3;
                        barData.baseScale += (barData.targetScale - barData.baseScale) * 0.1;

                        barData.mesh.scale.y = barData.baseScale;
                        barData.mesh.position.y = (barData.baseScale - 1) * 0.5;

                        // Update color
                        const hue = (index / this.frequencyBars.length + this.frameCount * 0.001) % 1;
                        barData.mesh.material.color.setHSL(hue, 1, 0.5 + frequency * 0.5);
                    });
                }
            }

            updateBeatRings() {
                const currentTime = performance.now();

                this.beatRingPool.forEach((ringData) => {
                    if (!ringData.active) return;

                    const elapsed = currentTime - ringData.startTime;
                    const progress = Math.min(elapsed / ringData.duration, 1);

                    if (progress >= 1) {
                        // Animation complete, deactivate ring
                        ringData.active = false;
                        ringData.material.opacity = 0;
                        return;
                    }

                    // Easing function for smooth animation (ease-out)
                    const easeOut = 1 - Math.pow(1 - progress, 3);

                    // Scale animation - rings expand outward
                    const currentScale = 0.5 + easeOut * 2.5; // From 0.5 to 3.0
                    ringData.mesh.scale.setScalar(currentScale);

                    // Opacity animation - fade out as they expand
                    const opacity = ringData.startOpacity * (1 - easeOut);
                    ringData.material.opacity = opacity;

                    // Vertical movement - rings rise up as they expand
                    const verticalOffset = easeOut * 8; // Rise up to 8 units
                    const sourceRing = this.bassRings[ringData.sourceRingIndex];
                    if (sourceRing) {
                        ringData.mesh.position.y = sourceRing.mesh.position.y + verticalOffset;
                    }

                    // Color animation - shift hue slightly over time
                    const hueShift = progress * 0.1;
                    const baseHue = ringData.colorHue;
                    ringData.material.color.setHSL(
                        (baseHue + hueShift) % 1,
                        1,
                        0.7 - progress * 0.3
                    );
                    ringData.material.emissive.setHSL(
                        (baseHue + hueShift) % 1,
                        1,
                        (0.3 - progress * 0.2) * (1 - progress)
                    );
                });
            }

            updateWobbleEffect() {
                if (!this.wobblePlane) return;

                const positions = this.wobblePlane.geometry.attributes.position.array;
                const time = this.frameCount * 0.01;

                for (let i = 0; i < positions.length; i += 3) {
                    const x = this.originalPositions[i];
                    const z = this.originalPositions[i + 2];

                    // Create wobble effect based on bass
                    const wobble = Math.sin(time + x * 0.1) * Math.cos(time + z * 0.1) * this.bassLevel * 5;
                    positions[i + 1] = this.originalPositions[i + 1] + wobble;
                }

                this.wobblePlane.geometry.attributes.position.needsUpdate = true;
            }

            updateLighting() {
                // Bass-reactive lighting
                this.bassLight1.intensity = 1 + this.bassLevel * 3;
                this.bassLight2.intensity = 1 + this.bassLevel * 3;
                this.highLight.intensity = 0.5 + this.highLevel * 2;

                // Strobe effect decay
                if (this.strobeLight.intensity > 0) {
                    this.strobeLight.intensity *= 0.9;
                }

                // Color cycling
                const time = this.frameCount * 0.01;
                this.bassLight1.color.setHSL((time * 0.1) % 1, 1, 0.5);
                this.bassLight2.color.setHSL((time * 0.1 + 0.5) % 1, 1, 0.5);
            }

            updateCameraShake() {
                if (this.cameraShake.intensity > 0) {
                    this.cameraShake.x = (Math.random() - 0.5) * this.cameraShake.intensity * 0.1;
                    this.cameraShake.y = (Math.random() - 0.5) * this.cameraShake.intensity * 0.1;
                    this.cameraShake.intensity *= 0.95;
                } else {
                    this.cameraShake.x = 0;
                    this.cameraShake.y = 0;
                }
                this.updateCameraPosition();
            }

            updateFPS() {
                const currentTime = performance.now();
                this.fps = Math.round(1000 / (currentTime - this.lastTime));
                this.lastTime = currentTime;

                if (this.frameCount % this.fpsUpdateInterval === 0) {
                    document.getElementById('fps').textContent = this.fps;
                }
            }

            animate() {
                requestAnimationFrame(() => this.animate());

                try {
                    this.frameCount++;

                    // Performance monitoring - reduce quality if FPS drops
                    if (this.frameCount % 60 === 0 && this.fps < 30) {
                        this.updateFrequency = Math.min(this.updateFrequency + 1, 5);
                        console.warn('⚠️ Low FPS detected, reducing update frequency');
                    } else if (this.frameCount % 120 === 0 && this.fps > 50 && this.updateFrequency > 2) {
                        this.updateFrequency = Math.max(this.updateFrequency - 1, 2);
                    }

                    this.updateFPS();
                    this.updateVisualization();

                    this.renderer.render(this.scene, this.camera);

                    // Reset frame counter to prevent overflow
                    if (this.frameCount > 100000) {
                        this.frameCount = 0;
                    }
                } catch (error) {
                    console.error('Animation error:', error);
                    // Continue animation even if there's an error
                }
            }
        }

        // Initialize the visualizer when the page loads
        window.addEventListener('load', () => {
            window.visualizer = new UltimateDubstepVisualizer();
        });
    </script>
</body>
</html>
